import json
from num2words import num2words
from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
from odoo.tools.misc import format_date
from datetime import datetime


class AccountMove(models.Model):
    _inherit = "account.move"

    def write(self, vals):
        for line in self.line_ids.filtered(lambda x: not x.company_id or x.company_currency_id):
            line.write({
                'company_id': self.company_id.id,
                'company_currency_id': self.company_id.currency_id.id,
            })
        res = super(AccountMove, self).write(vals)
        return res
<<<<<<< HEAD
=======

    def _move_autocomplete_invoice_lines_write(self, vals):
        enable_autocomplete = 'invoice_line_ids' in vals and 'line_ids' not in vals and True or False

        if not enable_autocomplete:
            return False

        vals['line_ids'] = vals.pop('invoice_line_ids')
        for invoice in self:
            invoice_new = invoice.with_context(default_move_type=invoice.move_type, default_journal_id=invoice.journal_id.id).new(origin=invoice)
            invoice_new.update(vals)
            values = invoice_new._move_autocomplete_invoice_lines_values()
            values.pop('invoice_line_ids', None)
            new_values = {k: v for k, v in values.items() if k in ['line_ids']}
            self.update_value_account_move(values)
            price_subtotal = 0
            for i in range(0, len(new_values['line_ids'])):
                if new_values['line_ids'][i][0] == 0:
                    stock_move_gr_match_ids = False
                    list_key = []
                    list_value = []
                    for k, v in new_values['line_ids'][i][2].items():
                        if k == 'price_subtotal':
                            if v > 0:
                                price_subtotal += v
                        if k == 'recompute_tax_line':
                            continue
                        if '[(6, 0' not in str(v):
                            list_key.append(k)
                            if k in ['name', 'date', 'date_maturity']:
                                if v == False:
                                    list_value.append('null')
                                else:
                                    if k == 'name':
                                        list_value.append("'{value}'".format(value=v.replace("'", "''")))
                                    else:
                                        list_value.append("'{value}'".format(value=v))
                            else:
                                if v == False:
                                    list_value.append('null')
                                else:
                                    list_value.append(str(v))
                        if k == 'stock_move_gr_match_ids':
                            stock_move_gr_match_ids = v[0][2][0]
                    qmarks1 = ', '.join(list_key)
                    qmarks2 = ', '.join(list_value)
                    query = "Insert Into account_move_line (%s, move_id)  Values (%s, %s) RETURNING ID" % (qmarks1, qmarks2, self.id)
                    self._cr.execute(query)
                    data_create = self._cr.fetchall()
                    if stock_move_gr_match_ids:
                        query = """
                        INSERT INTO stock_move_gr_match_rel (account_move_line_id, stock_move_id)
                        VALUES (%s, %s);
                        """
                        params = [data_create[0][0], stock_move_gr_match_ids]
                        cr = self._cr
                        cr.execute(query, params)
                if new_values['line_ids'][i][0] == 6:
                    # query = """
                    # SELECT * FROM account_move_line where move_id = %s;
                    # """
                    # params = [self.id]
                    # cr = self._cr
                    # tes = cr.execute(query, params)
                    # move = self.env['account.move.line'].search([('move_id', '=', self.id)])
                    # move.unlink()
                    query = """
                    DELETE FROM account_move_line where move_id = %s;
                    """
                    params = [self.id]
                    cr = self._cr
                    cr.execute(query, params)
                if new_values['line_ids'][i][0] == 1:
                    arr = []
                    for key, value in new_values['line_ids'][i][2].items():
                        # if type(value) is Datetime: # Handle TimeStamps
                        #     arr.append("{key} = {value}".format(key=key, value=value.timestamp()))
                        if type(value) is int: # Handle Integers
                            arr.append("{key} = {value}".format(key=key, value=value))
                        else: # Default Handler
                            arr.append("{key} = '{value}'".format(key=key, value=value))
                    data = ", ".join(arr)
                    query = "UPDATE account_move_line SET {data} WHERE id = {id};".format(data=data, id=self.id)
                    cr = self._cr
                    cr.execute(query)
            # invoice.write(new_values)
            query = "UPDATE account_move SET amount_total = {value}, amount_total_signed = {value}, amount_untaxed = {value}, amount_untaxed_signed = {value}, amount_residual = {value}, amount_residual_signed = {value} WHERE id = {id};".format(value=price_subtotal, id=self.id)
            cr = self._cr
            cr.execute(query)
            query = "UPDATE account_move_line SET debit = 0 WHERE debit isnull AND move_id = {id};".format(id=self.id)
            cr = self._cr
            cr.execute(query)
            query = "UPDATE account_move_line SET credit = 0 WHERE credit isnull AND move_id = {id};".format(id=self.id)
            cr = self._cr
            cr.execute(query)
        return True

    def update_value_account_move(self, values):
        if values.get('line_ids'):
            values.pop('line_ids', None)
        if values.get('adjustment_ids'):
            values.pop('adjustment_ids', None)
        for value in values:
            # if value != 'company_currency_id':
            if values[value] == False:
                query = "UPDATE account_move SET " + value + " = null WHERE id = %s;"
                params = [self.id]
            else:
                query = "UPDATE account_move SET " + value + " = %s WHERE id = %s;"
                params = [values[value], self.id]
            cr = self._cr
            cr.execute(query, params)

    # def set_price_unit_from_po(self):
    #     params = [self.id]
    #     query = """
    #     UPDATE account_move_line SET price_unit = pol.price_unit from purchase_order_line pol WHERE move_id = %s AND pol.id = purchase_line_id;
    #     """
    #     cr = self._cr
    #     cr.execute(query, params)
    #     self.line_ids._onchange_price_subtotal()
>>>>>>> f2db758ac4cd8d91ca423b800163ed827970c2e6

    # Proses untuk query
    # def _move_autocomplete_invoice_lines_write(self, vals):
    #     enable_autocomplete = 'invoice_line_ids' in vals and 'line_ids' not in vals and True or False
    #
    #     if not enable_autocomplete:
    #         return False
    #
    #     vals['line_ids'] = vals.pop('invoice_line_ids')
    #     for invoice in self:
    #         invoice_new = invoice.with_context(default_move_type=invoice.move_type, default_journal_id=invoice.journal_id.id).new(origin=invoice)
    #         invoice_new.update(vals)
    #         values = invoice_new._move_autocomplete_invoice_lines_values()
    #         values.pop('invoice_line_ids', None)
    #         new_values = {k: v for k, v in values.items() if k in ['line_ids']}
    #         self.update_value_account_move(values)
    #         price_subtotal = 0
    #         for i in range(0, len(new_values['line_ids'])):
    #             if new_values['line_ids'][i][0] == 0:
    #                 stock_move_gr_match_ids = False
    #                 list_key = []
    #                 list_value = []
    #                 for k, v in new_values['line_ids'][i][2].items():
    #                     if k == 'price_subtotal':
    #                         if v > 0:
    #                             price_subtotal += v
    #                     if k == 'recompute_tax_line':
    #                         continue
    #                     if '[(6, 0' not in str(v):
    #                         list_key.append(k)
    #                         if k in ['name', 'date', 'date_maturity']:
    #                             if v == False:
    #                                 list_value.append('null')
    #                             else:
    #                                 if k == 'name':
    #                                     list_value.append("'{value}'".format(value=v.replace("'", "''")))
    #                                 else:
    #                                     list_value.append("'{value}'".format(value=v))
    #                         else:
    #                             if v == False:
    #                                 list_value.append('null')
    #                             else:
    #                                 list_value.append(str(v))
    #                     if k == 'stock_move_gr_match_ids':
    #                         stock_move_gr_match_ids = v[0][2][0]
    #                 qmarks1 = ', '.join(list_key)
    #                 qmarks2 = ', '.join(list_value)
    #                 query = "Insert Into account_move_line (%s, move_id)  Values (%s, %s) RETURNING ID" % (qmarks1, qmarks2, self.id)
    #                 self._cr.execute(query)
    #                 data_create = self._cr.fetchall()
    #                 if stock_move_gr_match_ids:
    #                     query = """
    #                     INSERT INTO stock_move_gr_match_rel (account_move_line_id, stock_move_id)
    #                     VALUES (%s, %s);
    #                     """
    #                     params = [data_create[0][0], stock_move_gr_match_ids]
    #                     cr = self._cr
    #                     cr.execute(query, params)
    #             if new_values['line_ids'][i][0] == 6:
    #                 # query = """
    #                 # SELECT * FROM account_move_line where move_id = %s;
    #                 # """
    #                 # params = [self.id]
    #                 # cr = self._cr
    #                 # tes = cr.execute(query, params)
    #                 # move = self.env['account.move.line'].search([('move_id', '=', self.id)])
    #                 # move.unlink()
    #                 query = """
    #                 DELETE FROM account_move_line where move_id = %s;
    #                 """
    #                 params = [self.id]
    #                 cr = self._cr
    #                 cr.execute(query, params)
    #             if new_values['line_ids'][i][0] == 1:
    #                 arr = []
    #                 for key, value in new_values['line_ids'][i][2].items():
    #                     # if type(value) is Datetime: # Handle TimeStamps
    #                     #     arr.append("{key} = {value}".format(key=key, value=value.timestamp()))
    #                     if type(value) is int: # Handle Integers
    #                         arr.append("{key} = {value}".format(key=key, value=value))
    #                     else: # Default Handler
    #                         arr.append("{key} = '{value}'".format(key=key, value=value))
    #                 data = ", ".join(arr)
    #                 query = "UPDATE account_move_line SET {data} WHERE id = {id};".format(data=data, id=self.id)
    #                 cr = self._cr
    #                 cr.execute(query)
    #         # invoice.write(new_values)
    #         query = "UPDATE account_move SET amount_total = {value}, amount_total_signed = {value}, amount_untaxed = {value}, amount_untaxed_signed = {value}, amount_residual = {value}, amount_residual_signed = {value} WHERE id = {id};".format(value=price_subtotal, id=self.id)
    #         cr = self._cr
    #         cr.execute(query)
    #         query = "UPDATE account_move_line SET debit = 0 WHERE debit isnull AND move_id = {id};".format(id=self.id)
    #         cr = self._cr
    #         cr.execute(query)
    #         query = "UPDATE account_move_line SET credit = 0 WHERE credit isnull AND move_id = {id};".format(id=self.id)
    #         cr = self._cr
    #         cr.execute(query)
    #         query = "UPDATE account_move_line SET exclude_from_invoice_tab = false WHERE exclude_from_invoice_tab isnull AND move_id = {id};".format(id=self.id)
    #         cr = self._cr
    #         cr.execute(query)
    #         query = "UPDATE account_move_line SET subscription_mrr = 0 WHERE subscription_mrr isnull AND move_id = {id};".format(id=self.id)
    #         cr = self._cr
    #         cr.execute(query)
    #         query = "UPDATE account_move_line SET ref = '{ref}', journal_id = {journal_id}, create_date = '{create_date}', invoice_user_id = {invoice_user_id}, centralisation = 'normal' WHERE move_id = {id};".format(ref=self.ref, journal_id=self.journal_id.id, create_date=datetime.now(), invoice_user_id=self.invoice_user_id.id, id=self.id)
    #         cr = self._cr
    #         cr.execute(query)
    #     return True
    #
    # def update_value_account_move(self, values):
    #     if values.get('line_ids'):
    #         values.pop('line_ids', None)
    #     if values.get('adjustment_ids'):
    #         values.pop('adjustment_ids', None)
    #     for value in values:
    #         # if value != 'company_currency_id':
    #         if values[value] == False:
    #             query = "UPDATE account_move SET " + value + " = null WHERE id = %s;"
    #             params = [self.id]
    #         else:
    #             query = "UPDATE account_move SET " + value + " = %s WHERE id = %s;"
    #             params = [values[value], self.id]
    #         cr = self._cr
    #         cr.execute(query, params)
