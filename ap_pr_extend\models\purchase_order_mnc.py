from odoo import models, fields, api, _
from odoo.addons.ins_base_mnc.models.purchase import PurchaseOrder as PurchaseOrderMain


class PurchaseOrderinheritmnc(models.Model):
    _inherit = "purchase.order"

    @api.depends('order_line.price_total', 'pengurang')
    def _amount_all(self):
        for order in self:
            # amount_untaxed = 0.0
            # amount_tax = 0.0
            # for line in order.order_line:
            #     amount_untaxed = amount_untaxed + line.price_subtotal
            #     # amount_tax += line.price_tax
            # if order.order_line:
            #     tax_rate = order.order_line[0].taxes_id.amount / 100
            # else:
            #     tax_rate = 0.0
            # amount_tax = (amount_untaxed - order.pengurang) * tax_rate
            # currency = order.currency_id or order.partner_id.property_purchase_currency_id or self.env.company.currency_id
            # order.update({
            #     'amount_untaxed': currency.round(amount_untaxed - order.pengurang),
            #     'amount_tax': currency.round(amount_tax),
            #     'amount_total': amount_untaxed + amount_tax - order.pengurang,
            # })

            amount_untaxed = 0.0
            amount_tax = 0.0
<<<<<<< HEAD

            if order.pengurang > 0:
                for line in order.order_line:
                    amount_untaxed = amount_untaxed + line.price_subtotal
                if order.order_line:
                    tax_rate = order.order_line[0].taxes_id.amount / 100
                else:
                    tax_rate = 0.0

                amount_tax = (amount_untaxed - order.pengurang) * tax_rate
                currency = order.currency_id or order.partner_id.property_purchase_currency_id or self.env.company.currency_id
                order.update({
                    'amount_untaxed': currency.round(amount_untaxed - order.pengurang),
                    'amount_tax': currency.round(amount_tax),
                    'amount_total': amount_untaxed + amount_tax - order.pengurang,
                })
            else:
                for line in order.order_line:
                    subtotal_without_tax = line.price_subtotal
                    amount_untaxed += subtotal_without_tax
                    amount_tax += line.price_tax

                currency = order.currency_id or order.partner_id.property_purchase_currency_id or self.env.company.currency_id
                order.update({
                    'amount_untaxed': currency.round(amount_untaxed),
                    'amount_tax': currency.round(amount_tax),
                    'amount_total': amount_untaxed + amount_tax,
                })
=======
            list_tax = order.order_line.mapped('taxes_id')
            list_tax_all_line = []
            tax_rate_all_line = 0
            for tax in list_tax:
                all_line = True
                for line in order.order_line:
                    if type(tax.id) == int:
                        if tax.id not in line.taxes_id.ids:
                            all_line = False
                            break
                    else:
                        if tax.id.origin not in line.taxes_id.ids:
                            all_line = False
                            break
                if all_line:
                    list_tax_all_line.append(tax.id)
                    tax_rate_all_line += tax.amount
            for line in order.order_line:
                amount_untaxed = amount_untaxed + line.price_subtotal
                tax_rate = 0
                for tax in line.taxes_id.filtered(lambda x: x.id not in list_tax_all_line):
                    tax_rate += tax.amount
                amount_tax += line.price_subtotal * tax_rate / 100
                # for tax in line.taxes_id:
                #     list_tax.append(tax.id)
            # if order.order_line:
            #     tax_rate = order.order_line[0].taxes_id.amount / 100
            # else:
            #     tax_rate = 0.0
            # amount_tax = (amount_untaxed - order.pengurang) * tax_rate
            currency = order.currency_id or order.partner_id.property_purchase_currency_id or self.env.company.currency_id
            order.update({
                'amount_untaxed': currency.round(amount_untaxed - order.pengurang),
                'amount_tax': currency.round(amount_tax + (amount_untaxed - order.pengurang) * tax_rate_all_line / 100),
                'amount_total': amount_untaxed + amount_tax + (amount_untaxed - order.pengurang) * tax_rate_all_line / 100 - order.pengurang,
            })
>>>>>>> f2db758ac4cd8d91ca423b800163ed827970c2e6
            
            
    PurchaseOrderMain._amount_all = _amount_all