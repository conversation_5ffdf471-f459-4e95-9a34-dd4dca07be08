from odoo import models

class BalanceSheetTVONEReport(models.TransientModel):
    _inherit = "balance.sheet.tvone.report"

    def action_view(self):
<<<<<<< HEAD
        """Open the report in the account_reports style"""
        # Find the account_reports action
        action = None
        try:
            # Try the most common action ID
            action = self.env.ref('account_reports.action_account_report').read()[0]
        except ValueError:
            # If not found, search for any action that might be the account_reports action
            actions = self.env['ir.actions.client'].search([
                ('tag', '=', 'account_report'),
            ], limit=1)
            if actions:
                action = actions.read()[0]
            else:
                # Fallback to creating a new action
                action = {
                    'type': 'ir.actions.client',
                    'tag': 'account_report',
                    'name': 'Balance Sheet Detail TVONE',
                }

        # Set the context
        context = {
            'model': 'ap.balance.sheet.detail.report',
            'date': {
                'date_from': self.date_start.strftime('%Y-%m-%d'),
                'date_to': self.date.strftime('%Y-%m-%d'),
                'filter': 'custom',
                'string': f"{self.date_start.strftime('%d/%m/%Y')} - {self.date.strftime('%d/%m/%Y')}"
            },
            'active_id': self.id,
            'active_model': 'balance.sheet.tvone.report',
            'company_ids': self.company_ids.ids if self.company_ids else self.env.company.ids,
            'report_name': 'Balance Sheet Detail TVONE',
            'report_title': 'Balance Sheet Detail TVONE',
        }

        # Update the action
        action.update({
            'context': context,
            'name': 'Balance Sheet Detail TVONE',
            'display_name': 'Balance Sheet Detail TVONE',
            'title': 'Balance Sheet Detail TVONE',
            'breadcrumbs': [],  # Clear breadcrumbs
            'target': 'current',  # Ensure it replaces the current view
        })

        return action
=======
        url = '/get/report/balance_sheet/tvone/view/%s' % (self.id)
        if url:
            return {
                'type': 'ir.actions.act_url',
                'url': url,
                'target': 'self',
                'res_id': self.id,
            }
>>>>>>> f2db758ac4cd8d91ca423b800163ed827970c2e6
