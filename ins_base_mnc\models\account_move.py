import json
from num2words import num2words
from odoo import fields, models, api, _, exceptions
from odoo.exceptions import ValidationError
from odoo.tools.misc import format_date


class AccountMove(models.Model):
    _inherit = "account.move"
    _order = 'date desc, name desc, id asc'  # NOTE: change order

    amount_in_words = fields.Char('Amount To Words', compute='amount_to_text')
    amount_in_words_2 = fields.Char('Amount To Words 2', compute='amount_to_text_2')
    amount_due_in_words = fields.Char('Amount Due To Words', compute='amount_due_to_text')
    amount_due_in_words_2 = fields.Char('Amount Due To Words 2', compute='amount_due_to_text_2')
    purchase_vendor_bill_ids = fields.Many2many('purchase.order', 'bill_po_rel', string="Purchase Orders")
    purchase_picking_ids = fields.Many2many('stock.picking', 'bill_picking_rel', string="Receipts")
    bill_type = fields.Selection([
        ('standard', "Standard"), ('prepayment', "Prepayment")
    ], string="Bill Type", default='standard')
    purchase_request_id = fields.Many2one('purchase.request', string='PR No',
                                          domain=[("pr_type_second_id", "=", 1), ("state", "=", "approved"), ('is_prepayment_billed', '=', False)])
    prepayment_po_ref_id = fields.Many2one('purchase.order', string="Select PO Ref")
    prepayment_type_id = fields.Many2one('account.account', string="Prepayment Type",
                                         domain=[("user_type_id", 'in', [5, 7])])
    apply_prepayment_id = fields.Many2one('account.move', string="Apply", domain="[('bill_type', '=', 'prepayment')]")
    rate = fields.Float('Rate', compute='_compute_rate')
    ref = fields.Char('Description', copy=True)
    ref_desc = fields.Text('Description', compute='_compute_description', store=True)
    assignee_id_invoice = fields.Many2one('res.assignee.invoice', 'Assignee (invoice)', default=1)
    assignee_id_bill = fields.Many2one('res.assignee.bill', 'Assignee (bill)')
    po_numbers = fields.Char(string="PO Numbers", compute='_compute_po_numbers')
    po_numbers_store = fields.Char(string="PO Numbers", compute='_compute_po_numbers_store', store=True)
    employee = fields.Many2one('hr.employee', string="Employee")
    journal_reverse_id = fields.Many2one('account.move', compute='_compute_journal_reverse',
                                         string="Journal Reverse No")
    reconciliation_date = fields.Date(string="Reconciliation Date")
    ref_receipt = fields.Text('Receipt', compute='_compute_receipt_ref')
    ref_misc_receipt = fields.Text('Misc Receipt', compute='_compute_misc_receipt_ref')
    ref_payment = fields.Text('Payment', compute='_compute_payment_ref')
    ref_misc_payment = fields.Text('Misc Payment', compute='_compute_misc_payment_ref')
    amount_compare = fields.Float('Amount Compare')
    pph_id = fields.Many2one('account.tax', string="Pph")
    total_after_pph = fields.Float(string='Total yang harus dibayar', compute='_compute_pph23')
    pph23_amount = fields.Float(string='Pph amount', compute='_compute_pph23')
    settlement_state = fields.Selection([('paid', 'Paid'), ('unpaid', 'Unpaid'), ('settlement', 'Settlement')
                                         ], string="Settlement Status", default='unpaid', compute='_compute_settlement')
    apply_prepayment_ids = fields.Many2many(comodel_name='account.move', relation='prepayment_settlement_rel',
                                            column1='settlement_id',
                                            column2='prepayment_id', string="Prepayment")
    pr_number_id = fields.Many2one('purchase.request', string='PR.No')
    periode_start = fields.Date('Periode Start')
    periode_end = fields.Date('Periode End')
    bank_id = fields.Many2one('res.bank', 'Bank')
    is_idr = fields.Boolean('Is IDR', compute='compute_is_idr')
    description = fields.Char(string='Label')

    @api.model
    def create(self, vals):
        res = super(AccountMove, self).create(vals)
        if res.move_type == 'entry':
            for line in res.line_ids:
                if not line.account_id:
                    raise ValidationError('Account must be filled')
        return res

    def write(self, vals):
        res = super(AccountMove, self).write(vals)
        for rec in self:
            if rec.move_type == 'entry':
                for line in rec.line_ids:
                    if not line.account_id:
                        raise ValidationError('Account must be filled')
        return res

    @api.onchange('description')
    def _onchange_label(self):
        for line in self.line_ids:
            line.name = self.description

    def get_sequence_invoice(self):
        sequence_id = self.transaction_type_id.sequence_id
        return sequence_id.next_by_id()

    @api.depends('posted_before', 'state', 'journal_id', 'date', 'transaction_type_id')
    def _compute_name(self):
        result = super(AccountMove, self)._compute_name()
        for rec in self:
            if rec.move_type == 'out_invoice' and rec.journal_id.code == 'INV' and rec.transaction_type_id and rec.transaction_type_id.sequence_id:
                rec.name = self.get_sequence_invoice() 
        return result

    @api.depends('currency_id')
    def compute_is_idr(self):
        for rec in self:
            if rec.currency_id and rec.currency_id.name == 'IDR':
                rec.is_idr = True
            else:
                rec.is_idr = False

    @api.depends('invoice_line_ids')
    def _compute_settlement(self):
        for rec in self:
            if rec.bill_type == 'prepayment':
                settlement_state = 'unpaid'
                if rec.payment_state == 'paid':
                    settlement_state = 'paid'

                settlement_line = []

                for invoice_line in rec.invoice_line_ids:
                    settlement_line_id = self.env['account.move.line'].search(
                        [('prepayment_line_id', '=', invoice_line.id)])
                    print('=', settlement_line_id)
                    print('=', invoice_line.id)
                    if settlement_line_id:
                        settlement_line.append(True)
                    else:
                        settlement_line.append(False)

                if all(settlement_line):
                    settlement_state = 'settlement'
                rec.settlement_state = settlement_state
            else:
                rec.settlement_state = 'unpaid'

    @api.depends('amount_total', 'pph_id')
    def _compute_pph23(self):
        for rec in self:
            if rec.pph_id:
                taxes = rec.pph_id.compute_all(rec.amount_total, quantity=1.0)
                rec.total_after_pph = taxes['total_included']
                rec.pph23_amount = taxes['total_included'] - rec.amount_total
            else:
                rec.total_after_pph = rec.amount_total
                rec.pph23_amount = 0.0
            total_adj = 0
            for adj in rec.adjustment_ids:
                if adj.state == 'posted':
                    if adj.type_adjustment == 'additional':
                        total_adj += adj.total_amount
                    elif adj.type_adjustment == 'deduction':
                        total_adj -= adj.total_amount
            rec.total_after_pph += total_adj

    def copy(self, default=None):
        self.ensure_one()

        # NOTE: disable duplicate if type is Vendor Bills and if record has
        # stock_move_gr_match_ids
        gr_match = self.invoice_line_ids.mapped('stock_move_gr_match_ids')
        if gr_match and self.move_type in ('in_invoice', 'in_refund', 'in_receipt'):
            raise ValidationError('Cannot duplicate Vendor Bills that have GR Matching')

        default = dict(default or {})
        default.update({'ref': self.ref})

        return super(AccountMove, self).copy(default)

    @api.model
    def _default_operating_unit_id(self):
        """ override function to set to False """
        return False

    @api.onchange('purchase_request_id')
    def _onchange_pr(self):
        # account_move_lines = self.env["account.move.line"].search([("move_id", "=", self.id)])
        self.line_ids.unlink()

        amt = 0
        pr = self.env["purchase.request"].search([("id", "=", self.purchase_request_id.id)])
        if pr:
            self.partner_id = pr.employee_id.user_id.partner_id.id
            if not self.partner_id:
                vendor = self.env["res.partner"].search([("name", "=", 'Administrator'), ("type", "=", 'contact')])
                account_payable = vendor.property_account_payable_id.id
            else:
                account_payable = pr.employee_id.user_id.partner_id.property_account_payable_id.id
            pr_line = self.env["purchase.request.line"].search([("request_id", "=", pr.id)])

            # move_line = {
            #     'name': pr_line.name,
            #     # 'product_id': pr_line.product_id.id,
            #     # 'product_uom_id': pr_line.product_uom_id.id,
            #     # 'quantity': pr_line.product_qty,
            #     # 'price_unit': pr_line.original_price,
            #     # 'price_subtotal': pr_line.product_qty * pr_line.original_price,
            #     'debit': 0,
            #     'credit': pr_line.product_qty * pr_line.original_price,
            #     'balance': pr_line.product_qty * pr_line.original_price,
            #     'analytic_account_id': pr_line.analytic_account_id.id,
            #     'currency_id': pr_line.currency_id or self.env.user.company_id.currency_id.id,
            #     'date_maturity': self.invoice_date_due,
            #     'partner_id': self.partner_id.id,
            #     'account_id': pr_line.product_id.categ_id.property_stock_account_input_categ_id,
            # }

            move_line = {}
            invoice_line = []
            for rec in pr_line:
                price = rec.days_total * rec.episode * rec.product_qty * rec.original_price
                amt = amt + price
                move_line = {
                    'name': self.journal_id.name, #+ ' (' + self.purchase_request_id.name + ' - ' + self.partner_id.name + ')',
                    'price_unit': 0,
                    'credit': amt,
                    'analytic_account_id': rec.analytic_account_id.id,
                    'currency_id': rec.currency_id or self.env.user.company_id.currency_id.id,
                    'partner_id': self.partner_id.id,
                    'account_id': account_payable, # pr.employee_id.user_id.partner_id.property_account_payable_id.id,
                    'exclude_from_invoice_tab': True,
                }
                invoice_line.append((0, 0, {
                    'product_id': rec.product_id.id,
                    'name': rec.name,
                    'quantity': rec.product_qty,
                    'price_unit': rec.original_price,
                    'product_uom_id': rec.product_uom_id.id,
                }))

            self.line_ids = [(0, False, move_line)]

            move_line2 = {
                'name': self.journal_id.name, # + ' (' + self.purchase_request_id.name + ' - ' + self.partner_id.name + ')',
                'price_unit': 0,
                'debit': amt,
                'credit': 0,
                'currency_id': pr_line.currency_id or self.env.user.company_id.currency_id.id,
                'account_id': self.journal_id.default_account_id.id,
                'exclude_from_invoice_tab': False,
            }
            self.line_ids = [(0, False, move_line2)]
            self.invoice_line_ids = invoice_line
            self.line_ids = self.line_ids.filtered(lambda x: x.debit != 0 or x.credit != 0)

    @api.onchange('journal_id')
    def _onchange_journal(self):
        """ override onchange function to prevent OU setup """
        self.operating_unit_id = False

    @api.depends('ref')
    def _compute_description(self):
        for record in self:
            text = str(record.ref)
            record.ref_desc = text

    @api.depends('payment_state', 'state')
    def _compute_journal_reverse(self):
        for record in self:
            record.journal_reverse_id = False
            moves = self.env["account.move"].search([("reversed_entry_id", "!=", False)])
            for rec in moves:
                if rec.reversed_entry_id.id == record.id:
                    record.journal_reverse_id = rec.id

    def _compute_misc_receipt_ref(self):
        for record in self:
            record.ref_misc_receipt = ''
            receipts = self.env["miscellaneous.miscellaneous"].search([
                ("move_id.name", "=", record.name),
                ("move_id.state", "=", "posted")])
            applied_receipts = self.env["miscellaneous.miscellaneous"].search([
                ("applied_customer_move_id.name", "=", record.name),
                ("applied_customer_move_id.state", "=", "posted")])
            for receipt in receipts:
                rec_text = str(receipt.doc_reference)
            for applied in applied_receipts:
                app_text = str(applied.doc_reference)
            if applied_receipts:
                record.ref_misc_receipt = app_text
            if receipts:
                for receipt in receipts:
                    if receipt.receipt_type_id.type == 'receive':
                        record.ref_misc_receipt = rec_text
                    elif receipt.receipt_type_id.type == 'payment':
                        record.ref_misc_receipt = ''

    def _compute_misc_payment_ref(self):
        for record in self:
            record.ref_misc_payment = ''
            payment = self.env["miscellaneous.miscellaneous"].search([
                ("move_id.name", "=", record.name),
                ("move_id.state", "=", "posted")])
            for pay in payment:
                pay_text = str(pay.doc_reference)
                if pay.receipt_type_id.type == 'receive':
                    record.ref_misc_payment = ''
                elif pay.receipt_type_id.type == 'payment':
                    record.ref_misc_payment = pay_text

    def _compute_receipt_ref(self):
        for record in self:
            record.ref_receipt = ''
            receipts = self.env["account.payment"].search([
                ("move_id.name", "=", record.name),
                ("move_id.state", "=", "posted")])
            if receipts:
                for rec in receipts:
                    man_text = str(rec.payment_doc_id.name)
                    cek_text = str(rec.check_id.name)
                    gro_text = str(rec.giro_id.name)
                    if rec.partner_type == 'customer':
                        if rec.payment_method_id.name == 'Manual':
                            record.ref_receipt = man_text
                        elif rec.payment_method_id.name == 'Checks':
                            record.ref_receipt = cek_text
                        elif rec.payment_method_id.name == 'Giro':
                            record.ref_receipt = gro_text
                        else:
                            record.ref_receipt = ''
                    elif rec.partner_type == 'supplier':
                        record.ref_receipt = ''
            else:
                record.ref_receipt = ''

    def _compute_payment_ref(self):
        for record in self:
            record.ref_payment = ''
            payment = self.env["account.payment"].search([
                ("move_id.name", "=", record.name),
                ("move_id.state", "=", "posted")])
            if payment:
                for pay in payment:
                    man_text = str(pay.payment_doc_id.name)
                    cek_text = str(pay.check_id.name)
                    gro_text = str(pay.giro_id.name)
                    if pay.partner_type == 'customer':
                        record.ref_payment = ''
                    elif pay.partner_type == 'supplier':
                        if pay.payment_method_id.name == 'Manual':
                            record.ref_payment = man_text
                        elif pay.payment_method_id.name == 'Checks':
                            record.ref_payment = cek_text
                        elif pay.payment_method_id.name == 'Giro':
                            record.ref_payment = gro_text
                        else:
                            record.ref_payment = ''
            else:
                record.ref_payment = ''

    # @api.constrains('payment_reference')
    # def _check_payment_reference(self):
    #     """ constrains function to check payment reference """
    #     self.ensure_one()
    #     domain = [
    #         ('payment_reference', '=ilike', self.payment_reference),
    #         ('id', '!=', self.id),
    #     ]
    #     moves = self.env['account.move'].sudo().search(domain)
    #     if moves:  # other moves exist, join the name and show as error
    #         move_name_list = []
    #         for move in moves:
    #             if move.name:
    #                 move_name_list.append(move.name)
    #
    #         move_names = ', '.join(move_name_list)
    #         raise ValidationError('Payment Reference already exists in %s' % move_names)

    def _recompute_tax_lines(self, recompute_tax_base_amount=False):
        """ inherit function to add empty analytic assignment """
        res = super(AccountMove, self)._recompute_tax_lines(recompute_tax_base_amount)
        # find line_ids with empty analytic_account_id
        # find analytic with is_default true and in the same company
        domain = [('is_default', '=', True), ('company_id', '=', self.company_id.id)]
        analytic = self.env['account.analytic.account'].search(domain, limit=1)
        for x in self.line_ids.filtered(lambda x: not x.analytic_account_id):
            x.analytic_account_id = analytic.id
        return res

    @api.constrains('payment_reference', 'move_type', 'partner_id', 'journal_id', 'invoice_date', 'state')
    def _check_duplicate_supplier_reference(self):
        """ override function to use payment_reference instead of ref """
        moves = self.filtered(
            lambda move: move.state == 'posted' and move.is_purchase_document() and move.payment_reference)
        if not moves:
            return

        self.env["account.move"].flush([
            "payment_reference", "move_type", "invoice_date", "journal_id",
            "company_id", "partner_id", "commercial_partner_id",
        ])
        self.env["account.journal"].flush(["company_id"])
        self.env["res.partner"].flush(["commercial_partner_id"])

        # /!\ Computed stored fields are not yet inside the database.
        self._cr.execute('''
            SELECT move2.id
            FROM account_move move
            JOIN account_journal journal ON journal.id = move.journal_id
            JOIN res_partner partner ON partner.id = move.partner_id
            INNER JOIN account_move move2 ON
                move2.payment_reference = move.payment_reference
                AND move2.company_id = journal.company_id
                AND move2.commercial_partner_id = partner.commercial_partner_id
                AND move2.move_type = move.move_type
                AND (move.invoice_date is NULL)
                AND move2.id != move.id
            WHERE move.id IN %s
        ''', [tuple(moves.ids)])
        duplicated_moves = self.browse([r[0] for r in self._cr.fetchall()])
        if duplicated_moves:
            raise ValidationError(
                _('Duplicated vendor reference detected. You probably encoded twice the same vendor bill/credit note:\n%s') % "\n".join(
                    duplicated_moves.mapped(lambda m: "%(partner)s - %(payment_reference)s - %(date)s" % {
                        'payment_reference': m.payment_reference,
                        'partner': m.partner_id.display_name,
                        'date': format_date(self.env, m.invoice_date),
                    })
                ))

    @api.depends('invoice_line_ids')
    def _compute_po_numbers(self):
        for record in self:
            po_number_list = []
            for line in record.invoice_line_ids:
                if line.purchase_order_id:
                    po_number_list.append(line.purchase_order_id.name)

            po_number_list = list(set(po_number_list))
            po_number_list.sort()
            po_numbers = ', '.join(po_number_list)
            record.po_numbers = po_numbers

    @api.depends('po_numbers')
    def _compute_po_numbers_store(self):
        for record in self:
            record.po_numbers_store = record.po_numbers

    @api.depends('invoice_date', 'manual_currency_rate_active', 'manual_currency_rate')
    def _compute_rate(self):
        """ compute function to calculate rate based on date or manual """
        for rec in self:
            if rec.manual_currency_rate_active:
                rec.rate = rec.manual_currency_rate
            else:
                lines = rec.currency_id.rate_ids.filtered(
                    lambda x: x.name <= (rec.invoice_date or rec.date))
                rec.rate = lines[0].actual_rate if lines else 0

    @api.depends('amount_total', 'currency_id')
    def amount_to_text(self):
        for rec in self:
            if rec.currency_id:
                lang = 'id' if rec.currency_id.name == 'IDR' else 'en'
                lang = 'en'
                currency_in_words = rec.currency_id.currency_unit_label
                # convert to integer to remove decimal place
                words_amount = num2words(int(rec.amount_total), lang=lang)
                rec.amount_in_words = words_amount.title() + " " + currency_in_words
            else:
                rec.amount_in_words = ""

    @api.depends('amount_total', 'currency_id')
    def amount_to_text_2(self):
        for rec in self:
            if rec.currency_id:
                lang_2 = 'id' if rec.currency_id.name == 'IDR' else 'en'
                currency_in_words_2 = rec.currency_id.currency_unit_label
                # convert to integer to remove decimal place
                words_amount_2 = num2words(int(rec.amount_total), lang=lang_2)
                rec.amount_in_words_2 = words_amount_2.title() + " " + currency_in_words_2
            else:
                rec.amount_in_words_2 = ""

    @api.depends('amount_residual', 'currency_id')
    def amount_due_to_text_2(self):
        for rec in self:
            if rec.currency_id:
                lang = 'id' if rec.currency_id.name == 'IDR' else 'en'
                currency_in_words = rec.currency_id.currency_unit_label
                # convert to integer to remove decimal place
                words_amount = num2words(int(rec.amount_residual), lang=lang)
                rec.amount_due_in_words_2 = words_amount.title() + " " + currency_in_words
            else:
                rec.amount_due_in_words_2 = ""

    @api.depends('amount_residual', 'currency_id')
    def amount_due_to_text(self):
        for rec in self:
            if rec.currency_id:
                lang = 'id' if rec.currency_id.name == 'IDR' else 'en'
                currency_in_words = rec.currency_id.currency_unit_label
                # convert to integer to remove decimal place
                words_amount = num2words(int(rec.amount_residual), lang=lang)
                rec.amount_due_in_words = words_amount.title() + " " + currency_in_words
            else:
                rec.amount_due_in_words = ""

    @api.onchange('purchase_vendor_bill_id', 'purchase_id')
    def _onchange_purchase_auto_complete(self):
        """ inherit onchange function to set manual currency information """
        # purchase order found, set info
        if self.purchase_vendor_bill_id.purchase_order_id:
            po = self.purchase_vendor_bill_id.purchase_order_id
            self.manual_currency_rate_active = po.manual_currency_rate_active
            self.manual_currency_rate = po.manual_currency_rate

        # bypass self record to use context
        if self.manual_currency_rate_active:
            self = self.with_context(override_currency_rate=self.manual_currency_rate)

        res = super(AccountMove, self)._onchange_purchase_auto_complete()
        return res

    @api.onchange('apply_prepayment_id')
    def _onchange_apply_prepayment(self):
        for record in self:
            if record.apply_prepayment_id:
                for line in record.apply_prepayment_id.invoice_line_ids:
                    copied_vals = line.copy_data()[0]
                    copied_vals['quantity'] = -copied_vals['quantity']
                    copied_vals['move_id'] = self.id
                    copied_vals['prepayment_line_id'] = line.id
                    new_line = self.env['account.move.line'].new(copied_vals)
                    new_line._onchange_price_subtotal()
                    new_line.recompute_tax_line = True

                record._onchange_recompute_dynamic_lines()

                record.apply_prepayment_ids = [(4, record.apply_prepayment_id.id)]

                record.apply_prepayment_id = False

    @api.onchange('bill_type')
    def _onchange_bill_type(self):
        for record in self:
            if record.move_type == 'in_invoice':
                if record.bill_type == 'standard':
                    journal_id = self.env['account.journal'].search([
                        ('type', '=', 'purchase'), ('purchase_type', '=', 'purchase')], limit=1)
                    if journal_id:
                        record.journal_id = journal_id.id
                        record.name = False
                    else:
                        raise ValidationError(_("Vendor Bill Journal not found."))
                elif record.bill_type == 'prepayment':
                    journal_id = self.env['account.journal'].search([
                        ('type', '=', 'purchase'), ('purchase_type', '=', 'prepayment')], limit=1)
                    if journal_id:
                        record.journal_id = journal_id.id
                    else:
                        raise ValidationError(_("Prepayment Journal not found."))
                elif record.bill_type == 'settlement':
                    journal_id = self.env['account.journal'].search([
                        ('type', '=', 'purchase'), ('purchase_type', '=', 'settlement')], limit=1)
                    if journal_id:
                        record.journal_id = journal_id.id
                    else:
                        raise ValidationError(_("Settlement Journal not found."))

    @api.onchange('prepayment_po_ref_id')
    def _onchange_prepayment_po_ref(self):
        for record in self:
            if record.prepayment_po_ref_id:
                record.ref = record.prepayment_po_ref_id.name
            else:
                record.ref = False

            record.prepayment_po_ref_id = False

    def _has_different_currency(self):
        """ helper function to check if any different currency in line """
        curr_lines = [x.currency_id.id for x in self.invoice_line_ids if x.currency_id]
        curr_invoice = self.currency_id.id
        return curr_invoice not in curr_lines and len(curr_lines) != 0

    @api.onchange('date', 'currency_id')
    def _onchange_currency(self):
        """ override function to show warning if user changes currency """
        # check if there is any different currency in line, empty if yes
        payment_ref = self.payment_reference
        if self._has_different_currency():
            self.invoice_line_ids = [(2, x.id) for x in self.invoice_line_ids]
            self.line_ids = [(2, x.id) for x in self.line_ids]
        else:  # do as usual
            super(AccountMove, self)._onchange_currency()
        self.payment_reference = payment_ref

    def gr_matching(self):
        self.ensure_one()
        return {
            'name': _("GR Matching"),
            'type': 'ir.actions.act_window',
            'view_type': 'form',
            'view_mode': 'form',
            'res_model': 'gr.matching.wizard',
            'target': 'new',
            'context': {
                'default_bill_id': self.id,
                'default_partner_id': self.partner_id.id if self.partner_id else False,
                'default_company_id': self.company_id.id if self.company_id else False,
            },
        }

    def set_price_unit_from_po(self):
        self.invoice_line_ids.write_price_unit_from_po()

    def set_account_move_line_from_po(self, purchase_line_id, service_move):
        self.ensure_one()
        if purchase_line_id and self.operating_unit_id != purchase_line_id.operating_unit_id:
            raise exceptions.ValidationError(_("The operating unit of the purchase order must be the same as in the associated invoices."))
        # find move_ids from purchase line and get the first number
        pmoves = purchase_line_id.move_ids
        pmoves = pmoves.filtered(lambda x: x.line_number)
        pmoves = pmoves[0].line_number if pmoves and pmoves[0] else ''
        tax_ids = [(6, False, purchase_line_id.product_id.supplier_taxes_id.ids)] if \
            purchase_line_id.product_id.supplier_taxes_id else False
        move_line = {
            'sequence': purchase_line_id.sequence,
            'name': '%s: %s' % (
                purchase_line_id.order_id.name, purchase_line_id.name),
            'product_id': purchase_line_id.product_id.id,
            'product_uom_id': purchase_line_id.product_uom.id,
            'quantity': service_move.quantity_to_billed,
            'price_unit': purchase_line_id.price_unit,
            'analytic_account_id': purchase_line_id.account_analytic_id.id,
            'analytic_tag_ids': [(6, 0, purchase_line_id.analytic_tag_ids.ids)],
            'purchase_line_id': purchase_line_id.id,
            # 'asset_cost_progress_id': purchase_line_id.asset_cost_progress_id.id,
            'purchase_line_number': purchase_line_id.line_number,
            'picking_line_number': pmoves,
            'po_line_gr_match_ids': [(4, purchase_line_id.id)],
            'tax_ids': tax_ids,
        }

        currency = purchase_line_id.order_id.currency_id
        account_id = purchase_line_id.product_id.property_account_expense_id
        if not account_id:
            raise ValidationError(_("Expense Account for product {} not found.".format(
                purchase_line_id.product_id.name)))
        move_line.update({
            'currency_id': currency and currency.id or self.env.user.company_id.currency_id.id,
            'date_maturity': self.invoice_date_due,
            'partner_id': purchase_line_id.order_id.partner_id.id,
            'account_id': account_id.id,
        })

        self.invoice_line_ids = [(0, False, move_line)]

    def set_account_move_line_from_gr_line(self, stock_move, picking_move):
        self.ensure_one()
        if stock_move.purchase_line_id and self.operating_unit_id != stock_move.purchase_line_id.operating_unit_id:
            raise exceptions.ValidationError(_("The operating unit of the purchase order must be the same as in the associated invoices."))
        tax_ids = [(6, False, stock_move.product_id.supplier_taxes_id.ids)] if \
            stock_move.product_id.supplier_taxes_id else False
        move_line = {
            'sequence': stock_move.sequence,
            'name': '%s: %s' % (
                stock_move.picking_id.purchase_id.name, stock_move.name),
            'product_id': stock_move.product_id.id,
            'product_uom_id': stock_move.product_uom.id,
            'quantity': picking_move.quantity_to_billed,
            'price_unit': stock_move.purchase_line_id.price_unit,
            'analytic_account_id': stock_move.purchase_line_id.account_analytic_id.id,
            'analytic_tag_ids': [(6, 0, stock_move.purchase_line_id.analytic_tag_ids.ids)],
            'purchase_line_id': stock_move.purchase_line_id.id,
            'stock_picking_id': stock_move.picking_id.id,
            'stock_move_id': stock_move.id,
            # 'asset_cost_progress_id': stock_move.purchase_line_id.asset_cost_progress_id.id,
            'purchase_line_number': stock_move.purchase_line_id.line_number,
            'picking_line_number': stock_move.line_number,
            'stock_move_gr_match_ids': [(4, stock_move.id)],
            'tax_ids': tax_ids,
        }

        if self.currency_id == stock_move.picking_id.company_id.currency_id:
            currency = False
        else:
            currency = stock_move.picking_id.purchase_id.currency_id

        if stock_move.product_id.categ_id.property_stock_account_input_categ_id:
            account_id = stock_move.product_id.categ_id.property_stock_account_input_categ_id
        else:
            account_id = False

        if not account_id:
            raise ValidationError(_("Stock Input Account for product {} not found.".format(stock_move.product_id.name)))
        move_line.update({
            'currency_id': currency and currency.id or self.env.user.company_id.currency_id.id,
            'date_maturity': self.invoice_date_due,
            'partner_id': stock_move.picking_id.purchase_id.partner_id.id,
            'account_id': account_id.id,
        })

        payment_reference = self.payment_reference
        self.invoice_line_ids = [(0, 0, move_line)]
        self.payment_reference = payment_reference
        stock_move.is_gr_matched = True

    def gr_matching_add_past_bill(self, bill_line):
        self.ensure_one()
        copied_vals = bill_line.copy_data()[0]
        copied_vals['move_id'] = self.id
        copied_vals['quantity'] = -copied_vals['quantity']
        self.invoice_line_ids = [(0, False, copied_vals)]

    def gr_matching_final_set(self):
        self.ensure_one()
        origins = set(self.line_ids.mapped('purchase_line_id.order_id.name'))
        self.invoice_origin = ','.join(list(origins))

        refs = self._get_invoice_reference()
        self.ref = ', '.join(refs)

        self._onchange_currency()
        self.partner_bank_id = self.bank_partner_id.bank_ids and self.bank_partner_id.bank_ids[0]

    def _compute_payments_widget_to_reconcile_info(self):
        """ TOTAL OVERRIDE of function to add content """
        for move in self:
            move.invoice_outstanding_credits_debits_widget = json.dumps(False)
            move.invoice_has_outstanding = False

            if move.state != 'posted' \
                    or move.payment_state not in ('not_paid', 'partial') \
                    or not move.is_invoice(include_receipts=True):
                continue

            pay_term_lines = move.line_ids \
                .filtered(lambda line: line.account_id.user_type_id.type in ('receivable', 'payable'))

            domain = [
                ('account_id', 'in', pay_term_lines.account_id.ids),
                ('parent_state', '=', 'posted'),
                ('partner_id', '=', move.commercial_partner_id.id),
                ('reconciled', '=', False),
                '|', ('amount_residual', '!=', 0.0), ('amount_residual_currency', '!=', 0.0),
            ]

            payments_widget_vals = {'outstanding': True, 'content': [], 'move_id': move.id}

            if move.is_inbound():
                domain.append(('balance', '<', 0.0))
                payments_widget_vals['title'] = _('Outstanding credits')
            else:
                domain.append(('balance', '>', 0.0))
                payments_widget_vals['title'] = _('Outstanding debits')

            for line in self.env['account.move.line'].search(domain):

                if line.currency_id == move.currency_id:
                    # Same foreign currency.
                    amount = abs(line.amount_residual_currency)
                else:
                    # Different foreign currencies.
                    amount = move.company_currency_id._convert(
                        abs(line.amount_residual),
                        move.currency_id,
                        move.company_id,
                        line.date,
                    )

                if move.currency_id.is_zero(amount):
                    continue

                payments_widget_vals['content'].append({
                    'journal_name': line.ref or line.move_id.name,
                    'payment_references': '(%s)' % (line.payment_id.payment_id.multi_payment_reference) or '',
                    'amount': amount,
                    'currency': move.currency_id.symbol,
                    'id': line.id,
                    'move_id': line.move_id.id,
                    'position': move.currency_id.position,
                    'digits': [69, move.currency_id.decimal_places],
                    'payment_date': fields.Date.to_string(line.date),
                })

            if not payments_widget_vals['content']:
                continue

            move.invoice_outstanding_credits_debits_widget = json.dumps(payments_widget_vals)
            move.invoice_has_outstanding = True

    @api.onchange('invoice_line_ids')
    def _onchange_invoice_line_ids(self):
        payment_ref = self.payment_reference
        current_invoice_lines = self.line_ids.filtered(lambda line: not line.exclude_from_invoice_tab)
        others_lines = self.line_ids - current_invoice_lines
        if others_lines and current_invoice_lines - self.invoice_line_ids:
            others_lines[0].recompute_tax_line = True
        self.line_ids = others_lines + self.invoice_line_ids
        self._onchange_recompute_dynamic_lines()
        self.payment_reference = payment_ref

    def js_remove_outstanding_partial(self, partial_id):
        ''' Called by the 'payment' widget to remove a reconciled entry to the present invoice.

        :param partial_id: The id of an existing partial reconciled with the current invoice.
        '''
        self.ensure_one()
        # partial = self.env['account.partial.reconcile'].browse(partial_id)
        # return partial.unlink()
        return


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    stock_picking_id = fields.Many2one('stock.picking', string="Stock Picking")
    stock_move_id = fields.Many2one('stock.move', string="Stock Move")
    prepayment_line_id = fields.Many2one('account.move.line', store=True, string="Prepayment")
