# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError
from odoo.tools.float_utils import float_round


class GRMatchingWizard(models.TransientModel):
    _name = 'gr.matching.wizard'

    bill_id = fields.Many2one('account.move', string="Bill", required=True, ondelete='CASCADE')
    currency_id = fields.Many2one(related='bill_id.currency_id')
    partner_id = fields.Many2one('res.partner', string="Vendor", required=True)
    company_id = fields.Many2one('res.company', string="Company", required=True)
    purchase_order_ids = fields.Many2many('purchase.order', 'po_gr_match_rel', string="Purchase Order")
    picking_ids = fields.Many2many('stock.picking', 'picking_gr_match_rel', string="Goods Receipt",
                                   context={'is_return': True})
    total = fields.Monetary(string="Total", compute='_compute_total', store=True)
    stock_move_line_ids = fields.One2many('gr.matching.wizard.stock.move', 'gr_matching_id', string="Operations")

    @api.onchange('purchase_order_ids', 'picking_ids')
    def _onchange_picking_set_lines(self):
        for record in self:
            new_lines = []

            for po in record.purchase_order_ids:
                for order_line in po.order_line.filtered(lambda ol: ol.product_id.type == 'service' and not ol.is_gr_matched):
                    purchase_order_line_id = self.env['purchase.order.line'].browse(order_line._origin.id)

                    # Compute billed quantity
                    aml_pol_ids = self.env['account.move.line'].search([
                        ('move_id.cancel_reversal', '!=', True),
                        ('po_line_gr_match_ids', 'in', [order_line._origin.id])
                    ])

                    quantity_billed = sum([aml_pol.quantity for aml_pol in aml_pol_ids])
                    quantity_to_billed = purchase_order_line_id.qty_received - quantity_billed
                    price_subtotal = purchase_order_line_id.price_unit * quantity_to_billed
                    if stock_move_id.purchase_line_id and (stock_move_id.purchase_line_id.days_total > 0 or stock_move_id.purchase_line_id.number_episode > 0):
                        price_subtotal = (purchase_order_line_id.price_unit * quantity_to_billed) * purchase_order_line_id.days_total * purchase_order_line_id.number_episode


                    po_line_values = {
                        # 'purchase_order_line_id': order_line._origin.id,
                        'purchase_order_line_id': purchase_order_line_id.id,
                        'product_id': purchase_order_line_id.product_id.id,
                        'description_picking': purchase_order_line_id.name,
                        'price_unit': purchase_order_line_id.price_unit,
                        'price_subtotal': price_subtotal,
                        'product_uom_qty': purchase_order_line_id.product_qty,
                        'quantity_done': purchase_order_line_id.qty_received,
                        'quantity_billed': quantity_billed,
                        'quantity_to_billed': quantity_to_billed,
                    }
                    new_line = (0, False, po_line_values)
                    new_lines.append(new_line)

            for picking in record.picking_ids:
                for picking_line in picking.move_ids_without_package.filtered(lambda pl: pl.is_gr_matched is False):
                    stock_move_id = self.env['stock.move'].browse(picking_line._origin.id)

                    # Compute billed quantity
                    aml_sm_ids = self.env['account.move.line'].search([
                        ('move_id.cancel_reversal', '!=', True),
                        ('stock_move_gr_match_ids', 'in', [stock_move_id._origin.id])
                    ])

                    quantity_billed = sum([aml_sm.quantity for aml_sm in aml_sm_ids])
                    quantity_to_billed = stock_move_id.quantity_done - quantity_billed
                    price_subtotal = stock_move_id.purchase_line_id.price_unit * quantity_to_billed
                    if stock_move_id.purchase_line_id and (stock_move_id.purchase_line_id.days_total > 0 or stock_move_id.purchase_line_id.number_episode > 0):
                        price_subtotal = (stock_move_id.purchase_line_id.price_unit * quantity_to_billed) * stock_move_id.purchase_line_id.days_total * stock_move_id.purchase_line_id.number_episode


                    picking_line_values = {
                        'stock_move_id': picking_line._origin.id,
                        'product_id': stock_move_id.product_id.id,
                        'description_picking': stock_move_id.description_picking,
                        'price_unit': stock_move_id.purchase_line_id.price_unit if stock_move_id.purchase_line_id else 0.0,
                        'price_subtotal': price_subtotal,
                        'product_uom_qty': stock_move_id.product_uom_qty,
                        'quantity_done': stock_move_id.quantity_done,
                        'quantity_billed': quantity_billed,
                        'quantity_to_billed': quantity_to_billed,
                    }
                    new_line = (0, False, picking_line_values)
                    new_lines.append(new_line)

            record.stock_move_line_ids = [(5, False, False)]
            record.stock_move_line_ids = new_lines

    @api.depends(
        'stock_move_line_ids', 'stock_move_line_ids.select',
        'stock_move_line_ids.price_unit',
        'stock_move_line_ids.quantity_to_billed')
    def _compute_total(self):
        for rec in self:
            total = 0.0
            moves = rec.stock_move_line_ids.filtered(lambda sm: sm.select)
            for line in moves:
                if line.stock_move_id.purchase_line_id and (line.stock_move_id.purchase_line_id.days_total > 0 or line.stock_move_id.purchase_line_id.number_episode > 0):
                    total += (line.price_unit * line.quantity_to_billed) * line.stock_move_id.purchase_line_id.days_total * line.stock_move_id.purchase_line_id.number_episode
                else:
                    total += (line.price_unit * line.quantity_to_billed)
            rec.update({'total': total})

    def submit(self):
        self.ensure_one()
        gr_product_type = ['consu', 'product']
        bill_id = self.env['account.move'].browse(self.bill_id._origin.id)
        invoice_line_ids = []
        po_number_list = []
<<<<<<< HEAD
        po_list = []
=======
>>>>>>> f2db758ac4cd8d91ca423b800163ed827970c2e6

        for service_move in self.stock_move_line_ids.filtered(lambda sm: sm.select and sm.product_id.type == 'service'):
            qty_to_billed = service_move.quantity_done - service_move.quantity_billed
            if service_move.quantity_to_billed > qty_to_billed:
                raise UserError("The quantity to be billed is greater than the remaining quantity to be billed.")
            else:
                purchase_order_line_id = self.env['purchase.order.line'].browse(
                    service_move.purchase_order_line_id._origin.id)
<<<<<<< HEAD
                bill_id.set_account_move_line_from_po(purchase_order_line_id, service_move)
                # Proses untuk query
                # invoice_line_ids.append(bill_id.set_account_move_line_from_po(purchase_order_line_id, service_move))
                # bill_id.set_price_unit_from_po()
                # if purchase_order_line_id.order_id.name not in po_number_list:
                #     po_number_list.append(purchase_order_line_id.order_id.name)
                #     po_list.append(purchase_order_line_id.order_id)
=======
                # bill_id.set_account_move_line_from_po(purchase_order_line_id, service_move)
                invoice_line_ids.append(bill_id.set_account_move_line_from_po(purchase_order_line_id, service_move))
                bill_id.set_price_unit_from_po()
                if purchase_order_line_id.order_id.name not in po_number_list:
                    po_number_list.append(purchase_order_line_id.order_id.name)
>>>>>>> f2db758ac4cd8d91ca423b800163ed827970c2e6

        for picking_move in self.stock_move_line_ids.filtered(
                lambda sm: sm.select and sm.product_id.type in gr_product_type):
            qty_to_billed = picking_move.quantity_done - picking_move.quantity_billed
            if picking_move.quantity_to_billed > qty_to_billed:
                raise UserError("The quantity to be billed is greater than the remaining quantity to be billed.")
            else:
                stock_move_id = self.env['stock.move'].browse(picking_move.stock_move_id._origin.id)
<<<<<<< HEAD
                bill_id.set_account_move_line_from_gr_line(stock_move_id, picking_move)
                # Proses untuk query
                # invoice_line_ids.append(bill_id.set_account_move_line_from_gr_line(stock_move_id, picking_move))
                # bill_id.set_price_unit_from_po()
                # if stock_move_id.purchase_line_id.order_id.name not in po_number_list:
                #     po_number_list.append(stock_move_id.purchase_line_id.order_id.name)
                #     po_list.append(stock_move_id.purchase_line_id.order_id)
        # Proses untuk query
        # payment_reference = bill_id.payment_reference
        # bill_id.invoice_line_ids = invoice_line_ids
        # bill_id.payment_reference = payment_reference
        # po_number_list = list(set(po_number_list))
        # po_number_list.sort()
        # po_numbers = ', '.join(po_number_list)
        # bill_id.po_numbers_store = po_numbers
        # for po in po_list:
        #     po.invoice_ids = [(6, 0, po.invoice_ids.ids + [bill_id.id])]
=======
                # bill_id.set_account_move_line_from_gr_line(stock_move_id, picking_move)
                invoice_line_ids.append(bill_id.set_account_move_line_from_gr_line(stock_move_id, picking_move))
                bill_id.set_price_unit_from_po()
                if stock_move_id.purchase_line_id.order_id.name not in po_number_list:
                    po_number_list.append(stock_move_id.purchase_line_id.order_id.name)
        payment_reference = bill_id.payment_reference
        bill_id.invoice_line_ids = invoice_line_ids
        bill_id.payment_reference = payment_reference
        po_number_list = list(set(po_number_list))
        po_number_list.sort()
        po_numbers = ', '.join(po_number_list)
        bill_id.po_numbers_store = po_numbers

        # Recompute invoice count
        related_purchase_orders = self.purchase_order_ids
        for po in related_purchase_orders:
            po._compute_invoice()

>>>>>>> f2db758ac4cd8d91ca423b800163ed827970c2e6
        return True

    # def submit(self):
    #     self.ensure_one()
    #     gr_product_type = ['consu', 'product']
    #     bill_id = self.env['account.move'].browse(self.bill_id._origin.id)

    #     invoice_lines = []

    #     # --- Process Service Lines ---
    #     service_moves = self.stock_move_line_ids.filtered(
    #         lambda sm: sm.select and sm.product_id.type == 'service'
    #     )

    #     for service_move in service_moves:
    #         qty_to_billed = service_move.quantity_done - service_move.quantity_billed
    #         if service_move.quantity_to_billed > qty_to_billed:
    #             raise UserError("The quantity to be billed is greater than the remaining quantity to be billed.")

    #         purchase_order_line_id = self.env['purchase.order.line'].browse(
    #             service_move.purchase_order_line_id._origin.id
    #         )

    #         # Get move line dict from PO
    #         move_line_dict = bill_id.get_account_move_line_dict_from_po(purchase_order_line_id, service_move)
    #         invoice_lines.append((0, 0, move_line_dict))

    #     # --- Process GR (Goods Receipt) Lines ---
    #     picking_moves = self.stock_move_line_ids.filtered(
    #         lambda sm: sm.select and sm.product_id.type in gr_product_type
    #     )

    #     for picking_move in picking_moves:
    #         qty_to_billed = picking_move.quantity_done - picking_move.quantity_billed
    #         if picking_move.quantity_to_billed > qty_to_billed:
    #             raise UserError("The quantity to be billed is greater than the remaining quantity to be billed.")

    #         stock_move_id = self.env['stock.move'].browse(picking_move.stock_move_id._origin.id)

    #         # Get move line dict from GR line
    #         move_line_dict = bill_id.get_account_move_line_dict_from_gr_line(stock_move_id, picking_move)
    #         invoice_lines.append((0, 0, move_line_dict))

    #         stock_move_id.is_gr_matched = True  # Mark matched only after processing

    #     # Assign all invoice lines at once
    #     if invoice_lines:
    #         payment_reference = bill_id.payment_reference
    #         bill_id.invoice_line_ids = invoice_lines
    #         bill_id.payment_reference = payment_reference

    #     # Set price unit only once
    #     bill_id.set_price_unit_from_po()

    #     # Recompute invoice count
    #     related_purchase_orders = self.purchase_order_ids
    #     for po in related_purchase_orders:
    #         po._compute_invoice()
    #     return True


class GRMatchingWizardStockMove(models.TransientModel):
    _name = 'gr.matching.wizard.stock.move'

    gr_matching_id = fields.Many2one('gr.matching.wizard', string="GR Matching", required=True, ondelete='CASCADE')
    purchase_order_line_id = fields.Many2one('purchase.order.line', string="Purchase Order Line", ondelete='CASCADE')
    stock_move_id = fields.Many2one('stock.move', string="Operation", ondelete='CASCADE')
    product_id = fields.Many2one('product.product', string="Product")
    description_picking = fields.Text(string="Description")
    price_unit = fields.Monetary(string="Unit Price")
    price_subtotal = fields.Monetary(string="Subtotal")
    product_uom_qty = fields.Float(string="Demand")
    quantity_done = fields.Float(string="Done", digits='gr.matching.decimal.precision')
    quantity_billed = fields.Float(string="Billed", digits='gr.matching.decimal.precision')
    quantity_to_billed = fields.Float(string="To Billed", digits='gr.matching.decimal.precision')
    select = fields.Boolean(string="Select", default=False)
    currency_id = fields.Many2one('res.currency', string="Currency", related='gr_matching_id.currency_id')

    @api.onchange('price_subtotal')
    def _onchange_compute_quantity_to_billed(self):
        for rec in self:
            quantity_to_billed = 0
            if rec.price_subtotal > 0.0:
                quantity_to_billed = rec.price_subtotal / rec.price_unit
            if rec.stock_move_id.purchase_line_id.days_total > 0 or rec.stock_move_id.purchase_line_id.number_episode > 0:
                quantity_to_billed = rec.purchase_order_line_id.product_qty
            rec.quantity_to_billed = quantity_to_billed

    # @api.onchange('select')
    # def _onchange_select(self):
    #     for rec in self:
    #         if rec.select is True:
    #             rec.quantity_to_billed = rec.quantity_done - rec.quantity_billed

    @api.onchange('quantity_to_billed')
    def _onchange_compute_price_subtotal(self):
        for rec in self:
            price_subtotal = 0
            if rec.quantity_to_billed > 0.0:
                price_subtotal = rec.quantity_to_billed * rec.price_unit
            if rec.stock_move_id.purchase_line_id.days_total > 0 or rec.stock_move_id.purchase_line_id.number_episode > 0:
                price_subtotal = (rec.price_unit * rec.quantity_to_billed) * rec.stock_move_id.purchase_line_id.days_total * rec.stock_move_id.purchase_line_id.number_episode
            rec.price_subtotal = price_subtotal

    @api.constrains('quantity_to_billed')
    def _constraint_quantity_to_billed(self):
        for record in self:
            if record.select:
                if record.quantity_to_billed <= 0.0:
                    raise UserError("The quantity to be billed is 0.")
